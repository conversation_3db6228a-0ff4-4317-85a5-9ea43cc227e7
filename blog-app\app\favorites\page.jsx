'use client'
import { assets } from '@/Assets/assets';
import Footer from '@/Components/Footer';
import axios from 'axios';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const FavoritesPage = () => {
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const authToken = localStorage.getItem('authToken');
    if (!authToken) {
      router.push('/');
      return;
    }
    
    setIsLoggedIn(true);
    fetchFavorites();
  }, [router]);

  const fetchFavorites = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const response = await axios.get('/api/favorites', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setFavorites(response.data.favorites);
      } else {
        toast.error("Failed to load favorites");
      }
    } catch (error) {
      console.error("Error fetching favorites:", error);
      toast.error("Failed to load favorites");
    } finally {
      setLoading(false);
    }
  };

  const removeFavorite = async (blogId) => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await axios.delete(`/api/favorites?blogId=${blogId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        // Update the favorites list
        setFavorites(favorites.filter(blog => blog._id.toString() !== blogId));
        toast.success("Removed from favorites");
      } else {
        toast.error("Failed to remove from favorites");
      }
    } catch (error) {
      console.error("Error removing favorite:", error);
      toast.error("Failed to remove from favorites");
    }
  };

  return (
    <>
      <div className='bg-gray-200 py-5 px-5 md:px-12 lg:px-28'>
        <div className='flex justify-between items-center'>
          <Link href='/'>
            <Image src={assets.logo} width={180} alt='' className='w-[130px] sm:w-auto' />
          </Link>
          <Link href='/profile'>
            <button className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]'>
              Back to Profile
            </button>
          </Link>
        </div>
        
        <div className='text-center my-16'>
          <h1 className='text-3xl sm:text-5xl font-semibold'>My Favorite Blogs</h1>
          <p className='mt-4 text-gray-600'>Your collection of starred articles</p>
        </div>
      </div>

      <div className='container mx-auto px-4 py-8 mb-16'>
        {loading ? (
          <div className="flex justify-center items-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
          </div>
        ) : favorites.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {favorites.map(blog => (
              <div key={blog._id} className="bg-white rounded-lg overflow-hidden shadow-md border border-gray-200">
                <div className="relative h-48">
                  <Image 
                    src={blog.image} 
                    alt={blog.title} 
                    fill 
                    className="object-cover"
                  />
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <span className="inline-block px-3 py-1 text-sm bg-gray-100 rounded-full">
                      {blog.category}
                    </span>
                    <button 
                      onClick={() => removeFavorite(blog._id)}
                      className="text-yellow-500 hover:text-yellow-700"
                    >
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="20" 
                        height="20" 
                        viewBox="0 0 24 24" 
                        fill="currentColor"
                        stroke="currentColor" 
                        strokeWidth="2" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                      >
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                      </svg>
                    </button>
                  </div>
                  <h2 className="text-xl font-semibold mb-3 line-clamp-2">{blog.title}</h2>
                  <div 
                    className="text-gray-600 mb-4 line-clamp-3"
                    dangerouslySetInnerHTML={{
                      __html: blog.description.substring(0, 150) + '...'
                    }}
                  />
                  <Link 
                    href={`/blogs/${blog._id}`}
                    className="inline-block px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition"
                  >
                    Read Article
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-5xl mb-4">⭐</div>
            <h2 className="text-2xl font-semibold mb-2">No favorites yet</h2>
            <p className="text-gray-600 mb-6">Start adding blogs to your favorites by clicking the star icon on blog posts.</p>
            <Link 
              href="/"
              className="inline-block px-6 py-3 bg-black text-white rounded-md hover:bg-gray-800 transition"
            >
              Browse Blogs
            </Link>
          </div>
        )}
      </div>
      
      <Footer />
    </>
  );
};

export default FavoritesPage;