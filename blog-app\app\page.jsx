'use client'
import BlogList from "@/Components/BlogList";
import Footer from "@/Components/Footer";
import Header from "@/Components/Header";
import { ToastContainer } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import React, { useState, useEffect } from 'react';
import { trackPageView } from '@/utils/analyticsUtils';

export default function Home() {
  const [searchTerm, setSearchTerm] = useState("");
  
  useEffect(() => {
    // Track home page view
    trackPageView('/', 'home');
  }, []);
  
  return (
    <>
      <ToastContainer theme="dark"/>
      <Header setSearchTerm={setSearchTerm}/>
      <BlogList searchTerm={searchTerm}/>
      <Footer/>
    </>
  )
}




