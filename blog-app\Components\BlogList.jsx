import React, { useEffect, useState } from 'react'
import BlogItem from './BlogItem'
import axios from 'axios';

const BLOGS_PER_PAGE = 12;

const BlogList = ({ searchTerm = "" }) => {
    const [menu, setMenu] = useState("All");
    const [blogs, setBlogs] = useState([]);
    const [categories, setCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState(1);

    const fetchBlogs = async () => {
        try {
            const response = await axios.get('/api/blog');
            setBlogs(response.data.blogs);
            setLoading(false);
        } catch (error) {
            console.error("Error fetching blogs:", error);
            setLoading(false);
        }
    }

    const fetchCategories = async () => {
        try {
            const response = await axios.get('/api/categories');
            if (response.data.success && response.data.categories.length > 0) {
                setCategories(response.data.categories);
            } else {
                // Fallback to default categories if none found
                setCategories([
                    { _id: '1', name: 'Startup' },
                    { _id: '2', name: 'Technology' },
                    { _id: '3', name: 'Lifestyle' }
                ]);
            }
        } catch (error) {
            console.error("Error fetching categories:", error);
            // Fallback to default categories on error
            setCategories([
                { _id: '1', name: 'Startup' },
                { _id: '2', name: 'Technology' },
                { _id: '3', name: 'Lifestyle' }
            ]);
        }
    };

    useEffect(() => {
        fetchBlogs();
        fetchCategories();
    }, []);

    // Reset to first page when menu changes
    useEffect(() => {
        setCurrentPage(1);
    }, [menu]);

    // Filter blogs by category and search term
    const filteredBlogs = blogs.filter((item) => {
        const matchesCategory = menu === "All" ? true : item.category === menu;
        const matchesSearch = searchTerm.trim() === "" ? true : (
            item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
        return matchesCategory && matchesSearch;
    });
    const totalPages = Math.ceil(filteredBlogs.length / BLOGS_PER_PAGE);
    const startIdx = (currentPage - 1) * BLOGS_PER_PAGE;
    const currentBlogs = filteredBlogs.slice(startIdx, startIdx + BLOGS_PER_PAGE);

    return (
        <div>
            <div className='flex justify-center gap-6 my-10 flex-wrap'>
                <button 
                    onClick={() => setMenu('All')} 
                    className={menu === "All" ? 'bg-black text-white py-1 px-4 rounded-sm' : ''}
                >
                    All
                </button>
                
                {categories.map(category => (
                    <button 
                        key={category._id}
                        onClick={() => setMenu(category.name)} 
                        className={menu === category.name ? 'bg-black text-white py-1 px-4 rounded-sm' : ''}
                    >
                        {category.name}
                    </button>
                ))}
            </div>
            
            {loading ? (
                <div className="text-center py-10">Loading blogs...</div>
            ) : (
                <>
                    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-32 xl:mx-24'>
                        {currentBlogs.map((item, index) => (
                            <BlogItem 
                                key={index} 
                                id={item._id} 
                                image={item.image} 
                                title={item.title} 
                                description={item.description} 
                                category={item.category} 
                            />
                        ))}
                    </div>
                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="flex justify-center mt-0 mb-8 gap-2">
                            {Array.from({ length: totalPages }, (_, i) => (
                                <button
                                    key={i}
                                    onClick={() => setCurrentPage(i + 1)}
                                    className={`px-4 py-2 border border-black rounded ${currentPage === i + 1 ? 'bg-black text-white' : 'bg-white text-black hover:bg-gray-100'}`}
                                >
                                    {i + 1}
                                </button>
                            ))}
                        </div>
                    )}
                </>
            )}
        </div>
    )
}

export default BlogList
