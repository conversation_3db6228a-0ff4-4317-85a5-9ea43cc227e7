import { ConnectDB } from "@/lib/config/db";
import UserModel from "@/lib/models/UserModel";
import { NextResponse } from "next/server";
import { writeFile } from 'fs/promises';
import fs from 'fs';

// Initialize database connection
const LoadDB = async () => {
    await ConnectDB();
}
LoadDB();

// Get user profile
export async function GET(request) {
    try {
        const userId = request.nextUrl.searchParams.get("userId");
        
        if (!userId) {
            return NextResponse.json({
                success: false,
                message: "User ID is required"
            }, { status: 400 });
        }
        
        const user = await UserModel.findById(userId, { password: 0 }); // Exclude password
        
        if (!user) {
            return NextResponse.json({
                success: false,
                message: "User not found"
            }, { status: 404 });
        }
        
        return NextResponse.json({
            success: true,
            user: {
                id: user._id,
                email: user.email,
                role: user.role,
                profilePicture: user.profilePicture,
                name: user.name
            }
        });
    } catch (error) {
        console.error("Profile fetch error:", error);
        return NextResponse.json({
            success: false,
            message: "Server error"
        }, { status: 500 });
    }
}

// Update user profile
export async function PUT(request) {
    try {
        const formData = await request.formData();
        const userId = formData.get('userId');
        
        if (!userId) {
            return NextResponse.json({
                success: false,
                message: "User ID is required"
            }, { status: 400 });
        }
        
        // Find the user
        const user = await UserModel.findById(userId);
        if (!user) {
            return NextResponse.json({
                success: false,
                message: "User not found"
            }, { status: 404 });
        }
        
        // Update name if provided
        const name = formData.get('name');
        if (name) {
            user.name = name;
        }
        
        // Handle profile picture upload if provided
        const profilePicture = formData.get('profilePicture');
        if (profilePicture && profilePicture.name) {
            // Process new image
            const imageByteData = await profilePicture.arrayBuffer();
            const buffer = Buffer.from(imageByteData);
            const timestamp = Date.now();
            const path = `./public/profiles/${timestamp}_${profilePicture.name}`;
            
            // Ensure the directory exists
            if (!fs.existsSync('./public/profiles')) {
                fs.mkdirSync('./public/profiles', { recursive: true });
            }
            
            await writeFile(path, buffer);
            const imgUrl = `/profiles/${timestamp}_${profilePicture.name}`;
            
            // Delete old profile picture if it exists and is not the default
            if (user.profilePicture && user.profilePicture !== '/default_profile.png') {
                try {
                    fs.unlink(`./public${user.profilePicture}`, () => {});
                } catch (error) {
                    console.error("Error deleting old profile picture:", error);
                }
            }
            
            // Update profile picture URL
            user.profilePicture = imgUrl;
        }
        
        // Save the updated user
        await user.save();
        
        return NextResponse.json({
            success: true,
            message: "Profile updated successfully",
            user: {
                id: user._id,
                email: user.email,
                role: user.role,
                profilePicture: user.profilePicture,
                name: user.name
            }
        });
    } catch (error) {
        console.error("Profile update error:", error);
        return NextResponse.json({
            success: false,
            message: "Server error"
        }, { status: 500 });
    }
}