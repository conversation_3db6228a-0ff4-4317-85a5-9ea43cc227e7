import mongoose from "mongoose";

const Schema = new mongoose.Schema({
    filename: {
        type: String,
        required: true
    },
    path: {
        type: String,
        required: true
    },
    url: {
        type: String,
        required: true
    },
    contentType: {
        type: String,
        required: true
    },
    size: {
        type: Number,
        required: true
    },
    data: {
        type: Buffer,
        required: true
    },
    blogId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'blog',
        default: null
    },
    uploadDate: {
        type: Date,
        default: Date.now
    }
});

const ImageModel = mongoose.models.image || mongoose.model('image', Schema);

export default ImageModel;