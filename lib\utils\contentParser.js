// Utility functions for parsing blog content

// Parse blog content with mentions and images
export const parseBlogContent = (content) => {
  if (!content) return '';
  
  let parsedContent = content;
  
  // Regular expression to find blog mentions in format [[blogId|blogTitle]]
  const mentionRegex = /\[\[([a-f\d]{24})\|([^\]]+)\]\]/g;
  
  // Replace mentions with clickable links
  parsedContent = parsedContent.replace(mentionRegex, (match, blogId, blogTitle) => {
    return `<a href="/blogs/${blogId}" class="text-blue-600 hover:underline">${blogTitle}</a>`;
  });
  
  // Regular expression to find image tags in format {{imageId|imageUrl|altText}}
  const imageRegex = /\{\{([a-f\d]{24})\|([^|]+)\|([^}]+)\}\}/g;
  
  // Replace image tags with centered images
  parsedContent = parsedContent.replace(imageRegex, (match, imageId, imageUrl, altText) => {
    return `<div class="blog-image-container my-6 text-center">
      <img src="${imageUrl}" alt="${altText}" class="blog-content-image mx-auto rounded-lg shadow-md" style="max-width: 100%; height: auto; max-height: 400px;" />
      <p class="text-sm text-gray-500 mt-2 italic">${altText}</p>
    </div>`;
  });
  
  return parsedContent;
};

// Strip all special tags for plain text preview
export const stripBlogTags = (content) => {
  if (!content) return '';
  
  let strippedContent = content;
  
  // Remove blog mentions [[blogId|blogTitle]] -> blogTitle
  const mentionRegex = /\[\[([a-f\d]{24})\|([^\]]+)\]\]/g;
  strippedContent = strippedContent.replace(mentionRegex, (match, blogId, blogTitle) => {
    return blogTitle;
  });
  
  // Remove image tags {{imageId|imageUrl|altText}} -> [Image: altText]
  const imageRegex = /\{\{([a-f\d]{24})\|([^|]+)\|([^}]+)\}\}/g;
  strippedContent = strippedContent.replace(imageRegex, (match, imageId, imageUrl, altText) => {
    return `[Image: ${altText}]`;
  });
  
  return strippedContent;
};

// Get plain text content for previews (removes all HTML and special tags)
export const getPlainTextPreview = (content, maxLength = 120) => {
  if (!content) return '';
  
  // First strip our custom tags
  let plainText = stripBlogTags(content);
  
  // Remove any remaining HTML tags
  plainText = plainText.replace(/<[^>]*>/g, '');
  
  // Trim and truncate
  plainText = plainText.trim();
  if (plainText.length > maxLength) {
    plainText = plainText.substring(0, maxLength) + '...';
  }
  
  return plainText;
};
