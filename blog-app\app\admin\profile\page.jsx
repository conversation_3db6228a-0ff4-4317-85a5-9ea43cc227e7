'use client'
import axios from 'axios'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

const AdminProfilePage = () => {
    const router = useRouter();
    const [loading, setLoading] = useState(true);
    const [userData, setUserData] = useState({
        id: '',
        email: '',
        name: '',
        profilePicture: '/default_profile.png'
    });
    const [newProfilePicture, setNewProfilePicture] = useState(null);
    const [previewUrl, setPreviewUrl] = useState(null);
    const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
    const [showPasswordForm, setShowPasswordForm] = useState(false);
    
    // Add new state for password change
    const [passwordData, setPasswordData] = useState({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    });
    
    // Add logout functions
    const handleLogoutClick = () => {
        setShowLogoutConfirm(true);
    };

    const handleLogoutConfirm = () => {
        // Clear auth data from localStorage
        localStorage.removeItem('authToken');
        localStorage.removeItem('userRole');
        localStorage.removeItem('userId');
        localStorage.removeItem('userName');
        localStorage.removeItem('userProfilePicture');
        
        toast.success("Logged out successfully");
        router.push('/');
    };

    const handleLogoutCancel = () => {
        setShowLogoutConfirm(false);
    };
    
    useEffect(() => {
        const fetchUserData = async () => {
            try {
                const userId = localStorage.getItem('userId');
                if (!userId) {
                    router.push('/');
                    return;
                }

                const response = await axios.get('/api/profile', {
                    params: { userId }
                });

                if (response.data.success) {
                    setUserData(response.data.user);
                } else {
                    toast.error("Failed to load profile data");
                }
            } catch (error) {
                console.error("Profile fetch error:", error);
                toast.error("Failed to load profile data");
            } finally {
                setLoading(false);
            }
        };

        fetchUserData();
    }, [router]);

    const handleNameChange = (e) => {
        setUserData({
            ...userData,
            name: e.target.value
        });
    };

    const handleProfilePictureChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setNewProfilePicture(file);
            setPreviewUrl(URL.createObjectURL(file));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            const formData = new FormData();
            formData.append('userId', userData.id);
            formData.append('name', userData.name);
            
            if (newProfilePicture) {
                formData.append('profilePicture', newProfilePicture);
            }
            
            const response = await axios.put('/api/profile', formData);
            
            if (response.data.success) {
                // Update local storage with new profile data
                localStorage.setItem('userName', response.data.user.name);
                localStorage.setItem('userProfilePicture', response.data.user.profilePicture);
                
                // Dispatch a custom event to notify other components
                const profileUpdateEvent = new CustomEvent('profileUpdate', {
                    detail: {
                        name: response.data.user.name,
                        profilePicture: response.data.user.profilePicture
                    }
                });
                window.dispatchEvent(profileUpdateEvent);
                
                // Show success message
                toast.success("Profile updated successfully");
                
                // Update state with new data
                setUserData({
                    ...userData,
                    name: response.data.user.name,
                    profilePicture: response.data.user.profilePicture
                });
                
                // Clear file input
                setNewProfilePicture(null);
                setPreviewUrl(null);
            } else {
                toast.error(response.data.message || "Failed to update profile");
            }
        } catch (error) {
            console.error("Profile update error:", error);
            toast.error("Failed to update profile");
        }
    };

    // Handle password form input changes
    const handlePasswordChange = (e) => {
        setPasswordData({
            ...passwordData,
            [e.target.name]: e.target.value
        });
    };
    
    // Handle password form submission
    const handlePasswordSubmit = async (e) => {
        e.preventDefault();
        
        // Validate passwords match
        if (passwordData.newPassword !== passwordData.confirmPassword) {
            toast.error("New passwords do not match");
            return;
        }
        
        try {
            const response = await axios.put('/api/password', {
                userId: userData.id,
                currentPassword: passwordData.currentPassword,
                newPassword: passwordData.newPassword
            });
            
            if (response.data.success) {
                toast.success("Password updated successfully");
                // Reset form
                setPasswordData({
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                });
                // Hide form
                setShowPasswordForm(false);
            } else {
                toast.error(response.data.message || "Failed to update password");
            }
        } catch (error) {
            console.error("Password update error:", error);
            toast.error(error.response?.data?.message || "Failed to update password");
        }
    };

    if (loading) {
        return (
            <div className='pt-5 px-5 sm:pt-12 sm:pl-16'>
                <p>Loading profile data...</p>
            </div>
        );
    }

    return (
        <div className='pt-5 px-5 sm:pt-12 sm:pl-16'>
            <h1 className='text-2xl font-bold mb-6'>Admin Profile</h1>
            
            <div className='bg-white p-6 rounded-lg shadow-md'>
                <form onSubmit={handleSubmit} className='max-w-2xl'>
                    <div className='mb-6'>
                        <label className='block text-gray-700 mb-2'>Profile Picture</label>
                        <div className='flex items-center gap-4'>
                            <Image 
                                src={previewUrl || userData.profilePicture} 
                                width={100} 
                                height={100} 
                                alt='Profile' 
                                className='rounded-full object-cover w-24 h-24 border-2 border-gray-300'
                            />
                            <label className='cursor-pointer bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-md border border-gray-300'>
                                Change Picture
                                <input 
                                    type='file' 
                                    accept='image/*' 
                                    onChange={handleProfilePictureChange} 
                                    className='hidden' 
                                />
                            </label>
                        </div>
                    </div>
                    
                    <div className='mb-4'>
                        <label className='block text-gray-700 mb-2'>Email</label>
                        <input 
                            type='email' 
                            value={userData.email} 
                            disabled 
                            className='w-full px-4 py-3 border border-gray-300 rounded-md bg-gray-50' 
                        />
                    </div>
                    
                    <div className='mb-4'>
                        <label className='block text-gray-700 mb-2'>Display Name</label>
                        <input 
                            type='text' 
                            value={userData.name} 
                            onChange={handleNameChange} 
                            className='w-full px-4 py-3 border border-gray-300 rounded-md' 
                            placeholder='Enter your name'
                        />
                    </div>
                    
                    <div className='flex gap-4'>
                        <button 
                            type='submit' 
                            className='mt-4 w-40 h-12 bg-black text-white'
                        >
                            Save Changes
                        </button>
                        
                        <button 
                            type='button' 
                            onClick={() => router.push('/admin')}
                            className='mt-4 w-40 h-12 border border-black'
                        >
                            Cancel
                        </button>
                        
                        <button 
                            type='button' 
                            onClick={handleLogoutClick}
                            className='mt-4 w-40 h-12 bg-red-600 text-white'
                        >
                            Logout
                        </button>
                    </div>
                </form>
                
                {/* Password change form */}
                <div className='mt-12 border-t pt-6'>
                    <h2 className='text-xl font-semibold mb-4'>Change Password</h2>
                    <button 
                        type='button' 
                        onClick={() => setShowPasswordForm(!showPasswordForm)}
                        className='mb-4 px-4 py-2 border border-gray-300 rounded-md'
                    >
                        {showPasswordForm ? 'Cancel' : 'Change Password'}
                    </button>
                    
                    {showPasswordForm && (
                        <form onSubmit={handlePasswordSubmit} className='max-w-2xl'>
                            <div className='mb-4'>
                                <label className='block text-gray-700 mb-2'>Current Password</label>
                                <input 
                                    type='password'
                                    name='currentPassword'
                                    value={passwordData.currentPassword}
                                    onChange={handlePasswordChange}
                                    className='w-full px-4 py-3 border border-gray-300 rounded-md'
                                    required
                                />
                            </div>
                            
                            <div className='mb-4'>
                                <label className='block text-gray-700 mb-2'>New Password</label>
                                <input 
                                    type='password'
                                    name='newPassword'
                                    value={passwordData.newPassword}
                                    onChange={handlePasswordChange}
                                    className='w-full px-4 py-3 border border-gray-300 rounded-md'
                                    required
                                />
                            </div>
                            
                            <div className='mb-4'>
                                <label className='block text-gray-700 mb-2'>Confirm New Password</label>
                                <input 
                                    type='password'
                                    name='confirmPassword'
                                    value={passwordData.confirmPassword}
                                    onChange={handlePasswordChange}
                                    className='w-full px-4 py-3 border border-gray-300 rounded-md'
                                    required
                                />
                            </div>
                            
                            <button 
                                type='submit'
                                className='mt-4 w-40 h-12 bg-black text-white'
                            >
                                Update Password
                            </button>
                        </form>
                    )}
                </div>
            </div>
            
            {/* Logout Confirmation Modal */}
            {showLogoutConfirm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-md shadow-lg max-w-sm w-full">
                        <h3 className="text-lg font-semibold mb-4">Confirm Logout</h3>
                        <p className="mb-6">Are you sure you want to log out? You will need to log in again to access the admin panel.</p>
                        <div className="flex justify-end gap-3">
                            <button 
                                onClick={handleLogoutCancel}
                                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
                            >
                                Cancel
                            </button>
                            <button 
                                onClick={handleLogoutConfirm}
                                className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800"
                            >
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminProfilePage;



