@tailwind base;
@tailwind components;
@tailwind utilities;

/* Blog content image styles */
.blog-content {
  line-height: 1.8;
}

.blog-content .blog-image-container {
  margin: 2rem 0;
  text-align: center;
}

.blog-content .blog-content-image {
  max-width: 100%;
  height: auto;
  max-height: 400px;
  margin: 0 auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease-in-out;
}

.blog-content .blog-content-image:hover {
  transform: scale(1.02);
}

/* Responsive image sizing */
@media (max-width: 768px) {
  .blog-content .blog-content-image {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .blog-content .blog-content-image {
    max-height: 250px;
  }
}

/* Image caption styling */
.blog-content .blog-image-container p {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeInText {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-text-animate {
  animation: fadeInText 0.3s ease-out forwards;
  transform-origin: left center;
}

.scrollbar-hide::-webkit-scrollbar{
    display: none;
}

.scrollbar-hide{
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.blog-content{
    padding: 36px 0;
}

.blog-content p{
    margin-bottom: 10px;
}
.blog-content b{
    margin-bottom: 10px;
    font-weight: 500;
}
.blog-content h1{
    margin-bottom: 10px;
    font-size: 26px;
    font-weight: 600;
}
.blog-content h2{
    margin-bottom: 10px;
    font-size: 22px;
    font-weight: 500;
}
.blog-content h3{
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 500;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out forwards;
}

/* Add these carousel animation styles */
@keyframes slideLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

.carousel-slide {
  transition: transform 0.5s ease-in-out;
}

.carousel-item {
  flex-shrink: 0;
}

/* Add these carousel peek effect styles */
.carousel-peek-container {
  position: relative;
  overflow: hidden;
}

.carousel-peek-track {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.carousel-peek-item {
  flex-shrink: 0;
  padding-right: 16px;
}

/* Gradient fade effect for peeking items */
.carousel-peek-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 80px;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.8));
  pointer-events: none;
}


