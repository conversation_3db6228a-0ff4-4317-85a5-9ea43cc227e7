import { Outfit } from 'next/font/google'
import './globals.css'
import CookieConsent from '@/Components/CookieConsent'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'

// In production, use the pre-built CSS file
const isProd = process.env.NODE_ENV === 'production'
const cssPath = isProd ? '/build/tailwind.css' : './globals.css'

const outfit = Outfit({ subsets: ['latin'], weight: ["400", "500", "600", "700"] })

export const metadata = {
  title: 'Mr.Blogger',
  description: 'A blog platform by Mr.Blogger',
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        {isProd && <link rel="stylesheet" href={cssPath} />}
      </head>
      <body className={outfit.className}>
        {children}
        <CookieConsent />
        <ToastContainer position="top-center" autoClose={3000} />
      </body>
    </html>
  )
}



