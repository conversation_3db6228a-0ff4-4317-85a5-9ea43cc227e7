import { NextResponse } from 'next/server';
import { ConnectDB } from "@/lib/config/db";
import ImageModel from "@/lib/models/ImageModel";

export async function GET(request) {
  try {
    await ConnectDB();
    
    const blogId = request.nextUrl.searchParams.get("blogId");
    
    let query = {};
    if (blogId) {
      query.blogId = blogId;
    }
    
    // Get images but exclude the binary data to reduce response size
    const images = await ImageModel.find(query).select('-data');
    
    return NextResponse.json({ 
      success: true, 
      images: images
    });
    
  } catch (error) {
    console.error("Error retrieving images:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to retrieve images" 
    }, { status: 500 });
  }
}