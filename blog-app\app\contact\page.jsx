'use client'
import { assets } from '@/Assets/assets'
import axios from 'axios'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React, { useState, useEffect } from 'react'
import { ToastContainer, toast } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import Footer from '@/Components/Footer'

const ContactPage = () => {
  const router = useRouter()
  const [feedback, setFeedback] = useState({
    name: '',
    email: '',
    message: ''
  })
  const [loading, setLoading] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  })
  const [showPassword, setShowPassword] = useState(false)

  useEffect(() => {
    // Check if user is logged in
    const authToken = localStorage.getItem('authToken')
    const userId = localStorage.getItem('userId')
    
    if (!authToken || !userId) {
      setIsLoggedIn(false)
      setShowLoginModal(true)
      return
    }
    
    setIsLoggedIn(true)
    
    // Fetch user profile data to auto-fill form
    const fetchUserProfile = async () => {
      try {
        const response = await axios.get(`/api/profile?userId=${userId}`)
        
        if (response.data.success) {
          setFeedback({
            name: response.data.user.name || '',
            email: response.data.user.email || '',
            message: ''
          })
        }
      } catch (error) {
        console.error("Profile fetch error:", error)
      }
    }
    
    fetchUserProfile()
  }, [])

  const handleChange = (e) => {
    setFeedback({...feedback, [e.target.name]: e.target.value})
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!isLoggedIn) {
      setShowLoginModal(true)
      return
    }
    
    try {
      setLoading(true)
      const response = await axios.post('/api/feedback', feedback)
      if (response.data.success) {
        toast.success('Feedback submitted successfully')
        setFeedback({ ...feedback, message: '' })
      }
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to submit feedback')
    } finally {
      setLoading(false)
    }
  }

  const handleLoginChange = (e) => {
    setLoginData({...loginData, [e.target.name]: e.target.value})
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const handleLoginSubmit = async (e) => {
    e.preventDefault()
    try {
      const response = await axios.post('/api/auth', {
        email: loginData.email,
        password: loginData.password
      })
      
      if (response.data.success) {
        // Store auth data in localStorage
        localStorage.setItem('authToken', response.data.token || 'dummy-token')
        localStorage.setItem('userRole', response.data.user.role)
        localStorage.setItem('userId', response.data.user.id)
        
        toast.success("Login successful")
        setIsLoggedIn(true)
        setShowLoginModal(false)
        
        // Auto-fill the form with user data
        setFeedback({
          name: response.data.user.name || '',
          email: response.data.user.email || '',
          message: ''
        })
      } else {
        toast.error("Invalid credentials")
      }
    } catch (error) {
      console.error("Login error:", error)
      toast.error(error.response?.data?.message || "Login failed")
    }
  }

  return (
    <>
      <ToastContainer theme="dark" />
      <div className='bg-gray-200 py-5 px-5 md:px-12 lg:px-28'>
        <div className='flex justify-between items-center'>
          <Link href='/'>
            <Image src={assets.logo} width={180} alt='Mr.Blogger' className='w-[130px] sm:w-auto' />
          </Link>
          <Link href='/'>
            <button className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]'>
              Back to Home
            </button>
          </Link>
        </div>
        
        <div className='text-center my-16'>
          <h1 className='text-3xl sm:text-5xl font-semibold'>Contact Us</h1>
          <p className='mt-4 text-gray-600'>We'd love to hear from you. Send us a message!</p>
        </div>
      </div>

      {showLoginModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg max-w-md w-full">
            <h2 className="text-2xl font-bold mb-4">Login Required</h2>
            <p className="mb-4">Please login to submit feedback</p>
            
            <form onSubmit={handleLoginSubmit}>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Email</label>
                <input 
                  type="email" 
                  name="email"
                  value={loginData.email}
                  onChange={handleLoginChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div className="mb-6">
                <label className="block text-gray-700 mb-2">Password</label>
                <div className="relative">
                  <input 
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={loginData.password}
                    onChange={handleLoginChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600"
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                        <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
              <div className="flex justify-between">
                <button 
                  type="submit"
                  className="bg-black text-white px-4 py-2 rounded-md"
                >
                  Login
                </button>
                <Link href="/">
                  <button 
                    type="button"
                    className="text-gray-600 px-4 py-2"
                  >
                    Cancel
                  </button>
                </Link>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className='container mx-auto px-4 py-12 max-w-4xl'>
        <div className='bg-white rounded-lg shadow-md p-8'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
            <div>
              <h2 className='text-2xl font-semibold mb-4'>Get in Touch</h2>
              <p className='text-gray-600 mb-6'>
                Have questions, suggestions, or feedback? Fill out the form and we'll get back to you as soon as possible.
              </p>
              
              <div className='space-y-4'>
                <div className='flex items-center'>
                  <div className='bg-black p-3 rounded-full mr-4'>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className='font-medium'>Email</h3>
                    <p className='text-gray-600'><EMAIL></p>
                  </div>
                </div>
                
                <div className='flex items-center'>
                  <div className='bg-black p-3 rounded-full mr-4'>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className='font-medium'>Phone</h3>
                    <p className='text-gray-600'>+****************</p>
                  </div>
                </div>
                
                <div className='flex items-center'>
                  <div className='bg-black p-3 rounded-full mr-4'>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className='font-medium'>Address</h3>
                    <p className='text-gray-600'>123 Blog Street, Content City, 10001</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h2 className='text-2xl font-semibold mb-4'>Send a Message</h2>
              {isLoggedIn ? (
                <form onSubmit={handleSubmit} className='space-y-4'>
                  <div>
                    <label htmlFor="name" className='block text-sm font-medium text-gray-700 mb-1'>Your Name</label>
                    <input 
                      type='text' 
                      id='name'
                      name='name' 
                      placeholder='John Doe' 
                      value={feedback.name}
                      onChange={handleChange}
                      className='w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black'
                      required
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className='block text-sm font-medium text-gray-700 mb-1'>Your Email</label>
                    <input 
                      type='email' 
                      id='email'
                      name='email' 
                      placeholder='<EMAIL>' 
                      value={feedback.email}
                      onChange={handleChange}
                      className='w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black'
                      required
                      readOnly
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="message" className='block text-sm font-medium text-gray-700 mb-1'>Your Message</label>
                    <textarea 
                      id='message'
                      name='message' 
                      placeholder='How can we help you?' 
                      value={feedback.message}
                      onChange={handleChange}
                      className='w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black min-h-[150px]'
                      required
                    />
                  </div>
                  
                  <button 
                    type='submit' 
                    disabled={loading}
                    className='w-full bg-black text-white py-3 px-4 rounded-md font-medium hover:bg-gray-800 transition disabled:opacity-70'
                  >
                    {loading ? 'Sending...' : 'Send Message'}
                  </button>
                </form>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600 mb-4">Please log in to send us a message</p>
                  <button 
                    onClick={() => setShowLoginModal(true)}
                    className="bg-black text-white py-2 px-6 rounded-md font-medium hover:bg-gray-800 transition"
                  >
                    Login
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </>
  )
}

export default ContactPage
