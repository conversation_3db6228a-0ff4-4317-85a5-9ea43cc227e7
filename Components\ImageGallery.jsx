import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import axios from 'axios';
import { toast } from 'react-toastify';

const ImageGallery = ({ onImageSelect, onClose, showUpload = true }) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/images');
      if (response.data.success) {
        setImages(response.data.images);
      } else {
        toast.error('Failed to fetch images');
      }
    } catch (error) {
      console.error('Error fetching images:', error);
      toast.error('Failed to fetch images');
    } finally {
      setLoading(false);
    }
  };

  const handleImageSelect = (image) => {
    setSelectedImage(image);
  };

  const handleInsertImage = () => {
    if (selectedImage && onImageSelect) {
      onImageSelect(selectedImage);
      onClose();
    }
  };

  const handleUploadSuccess = () => {
    fetchImages(); // Refresh the gallery
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <div className="max-h-96 overflow-y-auto">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">Select an Image</h3>
        {images.length === 0 ? (
          <p className="text-gray-500 text-center py-8">
            No images uploaded yet. Upload your first image to get started.
          </p>
        ) : (
          <div className="grid grid-cols-3 gap-3">
            {images.map((image) => (
              <div
                key={image._id}
                className={`relative cursor-pointer border-2 rounded-lg overflow-hidden transition-all ${
                  selectedImage?._id === image._id
                    ? 'border-blue-500 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-400'
                }`}
                onClick={() => handleImageSelect(image)}
              >
                <Image
                  src={image.url}
                  alt={image.filename}
                  width={120}
                  height={80}
                  className="w-full h-20 object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all"></div>
                {selectedImage?._id === image._id && (
                  <div className="absolute top-1 right-1 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                    ✓
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="flex justify-between items-center pt-4 border-t">
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-600 hover:text-gray-800 transition"
        >
          Cancel
        </button>
        
        <div className="flex gap-2">
          {showUpload && (
            <button
              onClick={() => {
                // This will be handled by the parent component
                onClose();
                // Trigger upload modal
              }}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition"
            >
              Upload New
            </button>
          )}
          
          <button
            onClick={handleInsertImage}
            disabled={!selectedImage}
            className={`px-4 py-2 rounded transition ${
              selectedImage
                ? 'bg-blue-500 text-white hover:bg-blue-600'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Insert Image
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageGallery;
