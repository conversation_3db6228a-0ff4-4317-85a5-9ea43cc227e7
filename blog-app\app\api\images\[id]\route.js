import { NextResponse } from 'next/server';
import { ConnectDB } from "@/lib/config/db";
import ImageModel from "@/lib/models/ImageModel";

export async function GET(request, { params }) {
  try {
    await ConnectDB();
    
    const imageId = params.id;
    const image = await ImageModel.findById(imageId);
    
    if (!image) {
      return NextResponse.json({ 
        success: false, 
        message: "Image not found" 
      }, { status: 404 });
    }
    
    // Create response with appropriate content type
    const response = new NextResponse(image.data);
    response.headers.set('Content-Type', image.contentType);
    response.headers.set('Content-Length', image.size.toString());
    response.headers.set('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
    
    return response;
    
  } catch (error) {
    console.error("Error retrieving image:", error);
    return NextResponse.json({ 
      success: false, 
      message: "Failed to retrieve image" 
    }, { status: 500 });
  }
}