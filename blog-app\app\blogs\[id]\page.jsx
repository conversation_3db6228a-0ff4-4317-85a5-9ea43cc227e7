'use client'
import { assets, blog_data } from '@/Assets/assets';
import Footer from '@/Components/Footer';
import axios from 'axios';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useRouter } from 'next/navigation';
import { trackPageView } from '@/utils/analyticsUtils';
import TrendingBlogs from '@/Components/TrendingBlogs';

export default function BlogPost({ params }) {
  const router = useRouter();

  const [data, setData] = useState(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [isRegistering, setIsRegistering] = useState(false);
  const [registerData, setRegisterData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    role: "user" // Default role, not selectable by user
  });
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userRole, setUserRole] = useState("");
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);
  const [userProfilePicture, setUserProfilePicture] = useState("/default_profile.png");
  const [userName, setUserName] = useState("");
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [favoriteLoading, setFavoriteLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [likeLoading, setLikeLoading] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  const [trendingBlogs, setTrendingBlogs] = useState([]);

  const handleLoginChange = (e) => {
    setLoginData({...loginData, [e.target.name]: e.target.value});
  }

  const handleRegisterChange = (e) => {
    setRegisterData({...registerData, [e.target.name]: e.target.value});
  }

  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await axios.post('/api/auth', {
        email: loginData.email,
        password: loginData.password
      });
      
      if (response.data.success) {
        // Store auth data in localStorage
        localStorage.setItem('authToken', response.data.token || 'dummy-token');
        localStorage.setItem('userRole', response.data.user.role);
        localStorage.setItem('userId', response.data.user.id); // Add this line
        
        // Check if user is admin
        if (response.data.user.role === 'admin') {
          toast.success("Login successful");
          window.location.href = "/admin";
        } else {
          toast.success("Login successful");
          // Redirect regular users to homepage
          window.location.href = "/";
        }
      } else {
        toast.error("Invalid credentials");
      }
    } catch (error) {
      console.error("Login error:", error);
      toast.error(error.response?.data?.message || "Login failed");
    }
  }

  const handleRegisterSubmit = async (e) => {
    e.preventDefault();
    
    // Validate passwords match
    if (registerData.password !== registerData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }
    
    try {
      const response = await axios.post('/api/register', {
        email: registerData.email,
        password: registerData.password,
        role: registerData.role // Always "user" for public registration
      });
      
      if (response.data.success) {
        toast.success("Registration successful! Please login.");
        // Switch back to login form
        setIsRegistering(false);
        // Pre-fill email in login form
        setLoginData({...loginData, email: registerData.email});
        // Reset register form
        setRegisterData({
          email: "",
          password: "",
          confirmPassword: "",
          role: "user"
        });
      } else {
        toast.error(response.data.message || "Registration failed");
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast.error(error.response?.data?.message || "Registration failed");
    }
  }

  const toggleForm = () => {
    setIsRegistering(!isRegistering);
  }

  const fetchBlogData = async () => {
    const response = await axios.get('/api/blog', {
      params: {
        id: params.id
      }
    })
    setData(response.data);
  }

  useEffect(() => {
    fetchBlogData();
  }, [])

  useEffect(() => {
    const authToken = localStorage.getItem('authToken');
    const storedUserRole = localStorage.getItem('userRole');
    const storedProfilePicture = localStorage.getItem('userProfilePicture');
    const storedUserName = localStorage.getItem('userName');
    
    if (authToken) {
      setIsLoggedIn(true);
      setUserRole(storedUserRole || "user");
      if (storedProfilePicture) {
        setUserProfilePicture(storedProfilePicture);
      }
      if (storedUserName) {
        setUserName(storedUserName);
      }
    }
  }, []);

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
    setShowAccountDropdown(false);
  };

  const handleLogoutConfirm = () => {
    // Clear auth data from localStorage
    localStorage.removeItem('authToken');
    localStorage.removeItem('userRole');
    
    // Update state
    setIsLoggedIn(false);
    setUserRole("");
    setShowLogoutConfirm(false);
    
    toast.success("Logged out successfully");
  };

  const handleLogoutCancel = () => {
    setShowLogoutConfirm(false);
  };

  const toggleAccountDropdown = () => {
    setShowAccountDropdown(!showAccountDropdown);
  };

  const checkFavoriteStatus = async (token) => {
    try {
      const response = await axios.get(`/api/favorites?blogId=${params.id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setIsFavorite(response.data.isFavorite);
      }
    } catch (error) {
      console.error("Error checking favorite status:", error);
    }
  };

  const toggleFavorite = async () => {
    if (!isLoggedIn) {
      // Show login modal or redirect to login
      setShowLoginModal(true);
      return;
    }
    
    try {
      setFavoriteLoading(true);
      const token = localStorage.getItem('authToken');
      
      console.log("Using token:", token);
      
      if (isFavorite) {
        // Remove from favorites
        const response = await axios.delete(`/api/favorites?blogId=${params.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success) {
          setIsFavorite(false);
          toast.success("Removed from favorites");
        }
      } else {
        // Add to favorites
        const response = await axios.post('/api/favorites', 
          { blogId: params.id },
          { 
            headers: { 
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            } 
          }
        );
        
        if (response.data.success) {
          setIsFavorite(true);
          toast.success("Added to favorites");
        }
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);
      if (error.response?.status === 401) {
        toast.error("Please log in again to continue");
        localStorage.removeItem('authToken');
        setIsLoggedIn(false);
        setShowLoginModal(true);
      } else {
        toast.error("Failed to update favorites");
      }
    } finally {
      setFavoriteLoading(false);
    }
  };

  const checkLikeStatus = async (token) => {
    try {
      const response = await axios.get(`/api/likes?blogId=${params.id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setIsLiked(response.data.isLiked);
      }
    } catch (error) {
      console.error("Error checking like status:", error);
    }
  };

  const fetchLikesCount = async () => {
    try {
      const response = await axios.get(`/api/blog/likes?id=${params.id}`);
      if (response.data.success) {
        setLikesCount(response.data.count);
      }
    } catch (error) {
      console.error("Error fetching likes count:", error);
    }
  };

  const fetchTrendingBlogs = async () => {
    try {
      const response = await axios.get('/api/blog/trending');
      if (response.data.success) {
        setTrendingBlogs(response.data.blogs);
      }
    } catch (error) {
      console.error("Error fetching trending blogs:", error);
    }
  };

  const toggleLike = async () => {
    if (!isLoggedIn) {
      setShowLoginModal(true);
      return;
    }
    
    try {
      setLikeLoading(true);
      const token = localStorage.getItem('authToken');
      
      if (isLiked) {
        // Remove like
        const response = await axios.delete(`/api/likes?blogId=${params.id}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success) {
          setIsLiked(false);
          setLikesCount(prev => Math.max(0, prev - 1));
          toast.success("Like removed");
        }
      } else {
        // Add like
        const response = await axios.post('/api/likes', 
          { blogId: params.id },
          { 
            headers: { 
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            } 
          }
        );
        
        if (response.data.success) {
          setIsLiked(true);
          setLikesCount(prev => prev + 1);
          toast.success("Post liked");
        }
      }
    } catch (error) {
      console.error("Error toggling like:", error);
      if (error.response?.status === 401) {
        toast.error("Please log in again to continue");
        localStorage.removeItem('authToken');
        setIsLoggedIn(false);
        setShowLoginModal(true);
      } else {
        toast.error("Failed to update like");
      }
    } finally {
      setLikeLoading(false);
    }
  };

  useEffect(() => {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      setIsLoggedIn(true);
      checkFavoriteStatus(authToken);
      checkLikeStatus(authToken);
    }
    fetchLikesCount();
    fetchTrendingBlogs();
  }, []);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  useEffect(() => {
    // Track blog post view when data is loaded
    if (data && data._id) {
      trackPageView(`/blogs/${params.id}`, 'blog', data._id);
    }
  }, [data, params.id]);

  // Add this function to parse blog mentions in the description
  const parseBlogMentions = (content) => {
    if (!content) return '';
    
    // Regular expression to find blog mentions in format [[blogId|blogTitle]]
    const mentionRegex = /\[\[([a-f\d]{24})\|([^\]]+)\]\]/g;
    
    // Replace mentions with clickable links
    return content.replace(mentionRegex, (match, blogId, blogTitle) => {
      return `<a href="/blogs/${blogId}" class="text-blue-600 hover:underline">${blogTitle}</a>`;
    });
  };

  return (data ? <>
    <ToastContainer position="top-center" autoClose={3000} />
    <div className='bg-gray-200 py-5 px-5 md:px-12 lg:px-28'>
      <div className='flex justify-between items-center'>
        <Link href='/'>
          <Image src={assets.logo} width={180} alt='' className='w-[130px] sm:w-auto' />
        </Link>
        <div className='flex gap-3'>
          {isLoggedIn ? (
            <div className="relative">
              <button 
                onClick={() => router.push('/profile')}
                className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]'
              >
                <Image 
                  src={userProfilePicture} 
                  width={24} 
                  height={24} 
                  alt="Account" 
                  className="w-6 h-6 rounded-full object-cover"
                />
                <span>{userName || "Account"}</span>
              </button>
              
              {/* Removed dropdown menu */}
            </div>
          ) : (
            <button 
              onClick={() => setShowLoginModal(true)}
              className='flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]'
            >
              Get started <Image src={assets.arrow} alt="" />
            </button>
          )}
        </div>
      </div>
      <div className='text-center my-24'>
        <h1 className='text-2xl sm:text-5xl font-semibold max-w-[700px] mx-auto'>{data.title}</h1>
        <div className='flex justify-center mt-4'>
          <button 
            onClick={toggleFavorite}
            disabled={favoriteLoading}
            className={`flex items-center gap-2 px-4 py-2 rounded-full ${
              isFavorite 
                ? 'bg-yellow-100 text-yellow-700 border border-yellow-300' 
                : 'bg-gray-100 text-gray-700 border border-gray-300'
            }`}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill={isFavorite ? "currentColor" : "none"}
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            >
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            {isFavorite ? 'Favorited' : 'Add to Favorites'}
          </button>
        </div>
        <Image className='mx-auto mt-6 rounded-full object-cover w-16 h-16 border-2 border-white' src={data.authorImg} width={60} height={60} alt='' />
        <p className='mt-1 pb-2 text-lg max-w-[740px] mx-auto'>{data.author}</p>
      </div>
    </div>
    <div className='mx-5 max-w-[800px] md:mx-auto mt-[-100px] mb-10'>
      <Image className='border-4 border-white' src={data.image} width={800} height={480} alt='' />
      
      <div 
        className='blog-content' 
        dangerouslySetInnerHTML={{
          __html: parseBlogMentions(data.description)
        }}
      >
        
      </div>
      <div className="mt-8 flex items-center">
        <button 
          onClick={toggleLike}
          disabled={likeLoading}
          className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all ${
            isLiked 
              ? 'bg-red-100 text-red-600 border border-red-300' 
              : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
          }`}
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="20" 
            height="20" 
            viewBox="0 0 24 24" 
            fill={isLiked ? "currentColor" : "none"}
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>
          {isLiked ? 'Liked' : 'Like'}
        </button>
      </div>
      <div className='my-24'>
        <p className='text-black font font-semibold my-4'>Share this article on social media</p>
        <div className='flex'>
          <button
            onClick={() => { window.location.href = '/'; }}
            className="flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl"
            title="Share on Facebook"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path d="M22.675 0h-21.35C.595 0 0 .592 0 1.326v21.348C0 23.406.595 24 1.325 24h11.495v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.406 24 24 23.406 24 22.674V1.326C24 .592 23.406 0 22.675 0"/>
            </svg>
          </button>
          <button
            onClick={() => { window.location.href = '/'; }}
            className="flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full mr-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl"
            title="Share on Twitter"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path d="M24 4.557a9.83 9.83 0 0 1-2.828.775 4.932 4.932 0 0 0 2.165-2.724c-.951.564-2.005.974-3.127 1.195a4.916 4.916 0 0 0-8.38 4.482C7.691 8.095 4.066 6.13 1.64 3.161c-.542.929-.856 2.01-.857 3.17 0 2.188 1.115 4.116 2.823 5.247a4.904 4.904 0 0 1-2.229-.616c-.054 2.281 1.581 4.415 3.949 4.89a4.936 4.936 0 0 1-2.224.084c.627 1.956 2.444 3.377 4.6 3.417A9.867 9.867 0 0 1 0 21.543a13.94 13.94 0 0 0 7.548 2.209c9.057 0 14.009-7.496 14.009-13.986 0-.21 0-.423-.016-.634A9.936 9.936 0 0 0 24 4.557z"/>
            </svg>
          </button>
          <button
            onClick={() => { window.location.href = '/'; }}
            className="flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full shadow-lg transition-all border border-gray-200 hover:shadow-2xl"
            title="Share on Google Plus"
          >
            <svg width="20" height="20" viewBox="0 0 48 48" fill="black" xmlns="http://www.w3.org/2000/svg">
              <circle cx="24" cy="24" r="24" fill="black"/>
              <text x="13" y="32" font-size="18" font-family="Arial, Helvetica, sans-serif" fill="white" font-weight="bold">G+</text>
            </svg>
          </button>
          <button 
            onClick={() => {
              try {
                // Create a temporary input element
                const tempInput = document.createElement('input');
                // Set its value to the current URL
                tempInput.value = window.location.href;
                // Append it to the document
                document.body.appendChild(tempInput);
                // Select its content
                tempInput.select();
                // Execute the copy command
                document.execCommand('copy');
                // Remove the temporary element
                document.body.removeChild(tempInput);
                // Show success message with a more visible toast
                toast.success("Link copied to clipboard!", {
                  position: "top-center",
                  autoClose: 3000,
                  hideProgressBar: false,
                  closeOnClick: true,
                  pauseOnHover: true,
                  draggable: true,
                  progress: undefined,
                });
                console.log("Link copied to clipboard!");
              } catch (err) {
                console.error("Copy failed:", err);
                toast.error("Failed to copy link");
              }
            }}
            className="flex items-center justify-center w-[40px] h-[40px] bg-white rounded-full ml-3 shadow-lg transition-all border border-gray-200 hover:shadow-2xl"
            title="Copy link to clipboard"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="20" 
              height="20" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            >
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <TrendingBlogs blogs={trendingBlogs} currentBlogId={params.id} />
    <Footer />
    
    {/* Logout Confirmation Modal */}
    {showLogoutConfirm && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-md shadow-lg max-w-sm w-full">
          <h3 className="text-lg font-semibold mb-4">Confirm Logout</h3>
          <p className="mb-6">Are you sure you want to log out? You will need to log in again to access your account.</p>
          <div className="flex justify-end gap-3">
            <button 
              onClick={handleLogoutCancel}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
            >
              Cancel
            </button>
            <button 
              onClick={handleLogoutConfirm}
              className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    )}
    
    {/* Login Modal */}
    {showLoginModal && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-8 rounded-md shadow-lg w-96">
          <h2 className="text-2xl font-bold mb-4">{isRegistering ? "Register" : "Login"}</h2>
          
          {isRegistering ? (
            // Registration Form
            <form onSubmit={handleRegisterSubmit}>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Email</label>
                <input 
                  type="email" 
                  name="email"
                  value={registerData.email}
                  onChange={handleRegisterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Password</label>
                <input 
                  type="password" 
                  name="password"
                  value={registerData.password}
                  onChange={handleRegisterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Confirm Password</label>
                <input 
                  type="password" 
                  name="confirmPassword"
                  value={registerData.confirmPassword}
                  onChange={handleRegisterChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div className="flex justify-between">
                <button 
                  type="submit"
                  className="bg-black text-white px-4 py-2 rounded-md"
                >
                  Register
                </button>
                <button 
                  type="button"
                  onClick={() => setShowLoginModal(false)}
                  className="text-gray-600 px-4 py-2"
                >
                  Cancel
                </button>
              </div>
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  Already have an account?{" "}
                  <button 
                    type="button"
                    onClick={toggleForm}
                    className="text-blue-600 hover:underline"
                  >
                    Login
                  </button>
                </p>
              </div>
            </form>
          ) : (
            // Login Form
            <form onSubmit={handleLoginSubmit}>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Email</label>
                <input 
                  type="email" 
                  name="email"
                  value={loginData.email}
                  onChange={handleLoginChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
              <div className="mb-6">
                <label className="block text-gray-700 mb-2">Password</label>
                <div className="relative">
                  <input 
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={loginData.password}
                    onChange={handleLoginChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600"
                  >
                    {showPassword ? (
                      // Eye-slash icon (hidden)
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                        <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                      </svg>
                    ) : (
                      // Eye icon (visible)
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
              <div className="flex justify-between">
                <button 
                  type="submit"
                  className="bg-black text-white px-4 py-2 rounded-md"
                >
                  Login
                </button>
                <button 
                  type="button"
                  onClick={() => setShowLoginModal(false)}
                  className="text-gray-600 px-4 py-2"
                >
                  Cancel
                </button>
              </div>
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  Don't have an account?{" "}
                  <button 
                    type="button"
                    onClick={toggleForm}
                    className="text-blue-600 hover:underline"
                  >
                    Register
                  </button>
                </p>
              </div>
            </form>
          )}
        </div>
      </div>
    )}
  </> : null)
}
